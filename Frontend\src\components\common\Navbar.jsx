import { Link, useLocation, useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import "../../styles/Navbar.css";
import logo from "../../assets/images/XOsports-hub-logo.svg";
import Sidebar from "./Sidebar";
import RoleToggle from "./RoleToggle";
import { RiMenu5Line } from "react-icons/ri";
import AccountDropdown from "../buyer/AccountDropdown";
import SellerAccountDropdown from "../seller/SellerAccountDropdown";
import { logout, getUserForNavbar } from "../../redux/slices/authSlice";
import { handleBuyNavigation, handleSellNavigation } from "../../utils/navigationUtils";

const Navbar = () => {
  const location = useLocation();
  const path = location.pathname;
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [userData, setUserData] = useState(null);
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Get auth state from Redux
  const { user, isAuthenticated } = useSelector((state) => state.auth);

  // Fetch user data on component mount if authenticated
  useEffect(() => {
    const token = localStorage.getItem('xosportshub_token');
    if (token && !user) {
      dispatch(getUserForNavbar());
    }
  }, [dispatch, user]);

  // Update user data when auth state changes
  useEffect(() => {
    if (isAuthenticated && user) {
      setUserData(user);
      console.log("User data from API:", user);
    } else {
      setUserData(null);
      console.log("No user data found");
    }
  }, [user, isAuthenticated]);

  // Determine user role based on authentication state and user data
  let userRole = "visitor";
  let activeRole = null;

  if (userData && userData.role) {
    userRole = userData.role;
    // For non-admin users, use activeRole for navigation
    if (userData.role !== "admin") {
      activeRole = userData.activeRole || userData.role;
    }
  } else if (path.startsWith("/buyer")) {
    userRole = "buyer";
  } else if (path.startsWith("/seller")) {
    userRole = "seller";
  }

  // Use activeRole for navigation if available, otherwise use userRole
  const effectiveRole = activeRole || userRole;

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  // Handle logout
  const handleLogout = () => {
    dispatch(logout());
    navigate("/");
  };

  // Handle Buy tab click with authentication and role checks
  const handleBuyClick = (e) => {
    e.preventDefault();
    handleBuyNavigation(navigate);
  };

  // Handle Sell tab click with authentication and role checks
  const handleSellClick = (e) => {
    e.preventDefault();
    handleSellNavigation(navigate);
  };

  return (
    <>
      <nav className="navbar-component navbar">
        <div className="navbar-container max-container">
          <div className="navbar-logo">
            <Link to="/">
              <img src={logo} alt="XO Sports Hub Logo" />
            </Link>
          </div>

          {/* Hamburger menu for mobile */}
          <button className="navbar-toggle" onClick={toggleSidebar}>
            {sidebarOpen ? "✕" : <RiMenu5Line />}
          </button>

          {/* Desktop navigation links */}
          <div className="navbar-links">
            {effectiveRole === "visitor" && (
              // Visitor links
              <>
                <Link to="/" className={path === "/" ? "active" : ""}>
                  Home
                </Link>
                <a
                  href="#"
                  onClick={handleBuyClick}
                  className={path.startsWith("/buyer") ? "active" : ""}
                >
                  Buy
                </a>
                <a
                  href="#"
                  onClick={handleSellClick}
                  className={path.startsWith("/seller") ? "active" : ""}
                >
                  Sell
                </a>

                <Link
                  to="/contact"
                  className={path === "/contact" ? "active" : ""}
                >
                  Contact
                </Link>
              </>
            )}

            {(effectiveRole === "buyer" || effectiveRole === "seller") && userRole !== "admin" && (
              // Authenticated non-admin user links
              <>
                <Link to="/" className={path === "/" ? "active" : ""}>
                  Home
                </Link>
                <a
                  href="#"
                  onClick={handleBuyClick}
                  className={path.startsWith("/buyer") ? "active" : ""}
                >
                  Buy
                </a>
                <a
                  href="#"
                  onClick={handleSellClick}
                  className={path.startsWith("/seller") ? "active" : ""}
                >
                  Sell
                </a>

                <Link
                  to="/contact"
                  className={path === "/contact" ? "active" : ""}
                >
                  Contact
                </Link>
              </>
            )}

            {userRole === "admin" && (
              // Admin links
              <>
                <Link to="/" className={path === "/" ? "active" : ""}>
                  Home
                </Link>
                <Link
                  to="/contact"
                  className={path === "/contact" ? "active" : ""}
                >
                  Contact
                </Link>
              </>
            )}
          </div>

          {/* Role Toggle for non-admin authenticated users */}
          {userData && userRole !== "admin" && <RoleToggle />}

          {/* Desktop auth buttons */}
          <div className="navbar-auth">
            {effectiveRole === "visitor" && (
              <>
                <Link to="/auth" className="btn signinbtn">
                  Sign In
                </Link>
                <Link to="/signup" className="btn signupbtn">
                  Sign Up
                </Link>
              </>
            )}

            {effectiveRole === "buyer" && userRole !== "admin" && <AccountDropdown />}

            {effectiveRole === "seller" && userRole !== "admin" && <SellerAccountDropdown />}

            {userRole === "admin" && (
              <button className="btn btn-outline" onClick={handleLogout}>
                Logout
              </button>
            )}
          </div>
        </div>
      </nav>

      {/* Mobile Sidebar */}
      <Sidebar
        isOpen={sidebarOpen}
        toggleSidebar={toggleSidebar}
        userRole={userRole}
        effectiveRole={effectiveRole}
        userData={userData}
      />
    </>
  );
};

export default Navbar;

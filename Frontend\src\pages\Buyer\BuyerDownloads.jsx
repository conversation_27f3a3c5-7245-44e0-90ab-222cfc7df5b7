import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import {
  selectMyDownloads,
  selectLoading,
  selectErrors,
  fetchBuyerDownloads,
  clearError
} from "../../redux/slices/buyerDashboardSlice";
import { downloadContent } from "../../redux/slices/orderSlice";
import SectionWrapper from "../../components/common/SectionWrapper";
import LoadingSkeleton, { TableRowSkeleton } from "../../components/common/LoadingSkeleton";
import { ErrorDisplay } from "../../components/common/ErrorBoundary";
import { FaDownload, FaEye, FaSync } from "react-icons/fa";
import Table from "../../components/common/Table";
import { getImageUrl } from "../../utils/constants";
import "../../styles/BuyerDownloads.css";

const BuyerDownloads = () => {
  const dispatch = useDispatch();
  const downloads = useSelector(selectMyDownloads);
  const navigate = useNavigate();
  const loading = useSelector(selectLoading);
  const errors = useSelector(selectErrors);
  const [downloadingId, setDownloadingId] = useState(null);

  // Fetch downloads on component mount
  useEffect(() => {
    dispatch(fetchBuyerDownloads());
  }, [dispatch]);

  // Handle retry
  const handleRetry = () => {
    dispatch(clearError('downloads'));
    dispatch(fetchBuyerDownloads());
  };

  // Handle download
  const handleDownload = async (download) => {
    if (downloadingId === download.id) return;

    setDownloadingId(download.id);
    try {
      console.log('Starting download for order:', download.orderId);
      console.log('Download object:', download);
      const result = await dispatch(downloadContent(download.orderId)).unwrap();
      console.log('Download result:', result);
      console.log('Download result structure:', JSON.stringify(result, null, 2));

      if (result?.data?.downloadUrl) {
        const downloadUrl = result.data.downloadUrl;
        console.log('Download URL:', downloadUrl);

        // Get file extension from the original content or URL
        const fileExtension = getFileExtension(downloadUrl, download.fileType);
        const fileName = `${download.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}${fileExtension}`;

        // Try different download approaches
        try {
          if (downloadUrl.startsWith('http')) {
            // For external URLs (S3, etc.), try fetch approach first
            try {
              await downloadFileFromUrl(downloadUrl, fileName);
              toast.success('Download started successfully!');
            } catch (fetchError) {
              console.log('Fetch download failed, trying direct link:', fetchError);
              // Fallback to direct link approach
              downloadWithDirectLink(downloadUrl, fileName);
              toast.success('Download started successfully!');
            }
          } else {
            // For local files, use direct link approach
            const fullUrl = downloadUrl.startsWith('/') ?
              `${window.location.origin}${downloadUrl}` :
              `${window.location.origin}/${downloadUrl}`;
            downloadWithDirectLink(fullUrl, fileName);
            toast.success('Download started successfully!');
          }
        } catch (directDownloadError) {
          console.log('Direct download failed, trying streaming endpoint:', directDownloadError);
          // Final fallback: use streaming endpoint
          const streamUrl = `${window.location.origin}/api/orders/${download.orderId}/stream`;
          const token = localStorage.getItem('xosportshub_token');

          if (token) {
            downloadWithAuth(streamUrl, fileName, token);
            toast.success('Download started successfully!');
          } else {
            throw new Error('Authentication required for download');
          }
        }
      } else {
        console.error('No download URL in response:', result);
        toast.error('Download URL not available');
      }
    } catch (error) {
      console.error('Download error:', error);
      toast.error(error.message || 'Failed to download content. Please try again.');
    } finally {
      setDownloadingId(null);
    }
  };

  // Helper function to get file extension
  const getFileExtension = (url, fileType) => {
    // Try to get extension from URL first
    const urlExtension = url.split('.').pop()?.split('?')[0];
    if (urlExtension && urlExtension.length <= 4) {
      return `.${urlExtension}`;
    }

    // Fallback to file type mapping
    const typeExtensions = {
      'Video': '.mp4',
      'PDF': '.pdf',
      'Audio': '.mp3',
      'Image': '.jpg',
      'Document': '.pdf',
      'Text': '.txt'
    };

    return typeExtensions[fileType] || '';
  };

  // Download using fetch API (for CORS-enabled files)
  const downloadFileFromUrl = async (url, fileName) => {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const blob = await response.blob();
    const downloadUrl = window.URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Clean up the object URL
    window.URL.revokeObjectURL(downloadUrl);
  };

  // Download using direct link (fallback method)
  const downloadWithDirectLink = (url, fileName) => {
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    link.target = '_blank';
    link.rel = 'noopener noreferrer';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Download with authentication (for streaming endpoint)
  const downloadWithAuth = (url, fileName, token) => {
    // Create a temporary form to handle authenticated download
    const form = document.createElement('form');
    form.method = 'GET';
    form.action = url;
    form.target = '_blank';

    // Add authorization header via hidden input (for some servers)
    const authInput = document.createElement('input');
    authInput.type = 'hidden';
    authInput.name = 'authorization';
    authInput.value = `Bearer ${token}`;
    form.appendChild(authInput);

    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);

    // Alternative approach: open in new window with auth header
    const authUrl = `${url}?token=${encodeURIComponent(token)}`;
    window.open(authUrl, '_blank');
  };

  const columns = [
    {
      key: "no",
      label: "No.",
      className: "no",
    },
    {
      key: "orderId",
      label: "Order Id",
      className: "order-id",
    },
    {
      key: "video",
      label: "Videos/Documents",
      className: "video",
    },
    {
      key: "date",
      label: "Date",
      className: "date",
    },
    {
      key: "amount",
      label: "Amount",
      className: "amount",
    },
    {
      key: "status",
      label: "Status",
      className: "status",
    },
    {
      key: "action",
      label: "Action",
      className: "action",
    },
  ];

  const renderRow = (download, index) => (
    <>
      <div className="table-cell no">{index + 1}</div>
      <div className="table-cell order-id">#{download.orderId?.slice(-8) || download.id}</div>
      <div className="table-cell video">
        <div className="content-item">
          <div className="content-image">
            <img
              src={getImageUrl(download.thumbnail) || "https://images.unsplash.com/photo-1566577739112-5180d4bf9390?q=80&w=300&h=200&auto=format&fit=crop"}
              alt={download.title}
            />
          </div>
          <div className="content-info">
            <div className="content-title">{download.title}</div>
            <div className="content-coach">By {download.coach}</div>
            <div className="content-type">{download.fileType} • {download.fileSize}</div>
          </div>
        </div>
      </div>
      <div className="table-cell date">{download.downloadDate}</div>
      <div className="table-cell amount">${download.amount?.toFixed(2) || '0.00'}</div>
      <div className="table-cell status">
        <span className="status-badge downloaded">
          {download.downloadCount > 0 ? `Downloaded (${download.downloadCount}x)` : 'Available'}
        </span>
      </div>
      <div className="table-cell action">
        <div className="action-buttons">
          <button
            className="action-btn download-btn"
            onClick={() => handleDownload(download)}
            disabled={downloadingId === download.id}
            title="Download Content"
          >
            {downloadingId === download.id ? (
              <div className="spinner"></div>
            ) : (
              <FaDownload />
            )}
          </button>
          <button
            className="action-btn view-btn"
            onClick={() => navigate(`/buyer/download-details/${download.id}`)}
            title="View Details"
          >
            <FaEye />
          </button>
        </div>
      </div>
    </>
  );

  // Debug: Log downloads data
  console.log('Downloads data:', downloads);
  console.log('Downloads length:', downloads.length);
  console.log('Loading state:', loading.downloads);
  console.log('Errors:', errors.downloads);

  return (
    <div className="BuyerDownloads">
      <SectionWrapper
        icon={<FaDownload className="BuyerSidebar__icon" />}
        title="My Downloads"
        action={
          errors.downloads && (
            <button
              className="retry-btn"
              onClick={handleRetry}
              title="Retry loading downloads"
            >
              <FaSync />
            </button>
          )
        }
      >
        {errors.downloads ? (
          <ErrorDisplay
            error={errors.downloads}
            onRetry={handleRetry}
            title="Failed to load downloads"
          />
        ) : loading.downloads ? (
          <div className="loading-container">
            <TableRowSkeleton columns={7} />
            <TableRowSkeleton columns={7} />
            <TableRowSkeleton columns={7} />
          </div>
        ) : downloads.length > 0 ? (
          <Table
            columns={columns}
            data={downloads}
            renderRow={renderRow}
            variant="grid"
            gridTemplate="0.5fr 1fr 3fr 1.5fr 1fr 1fr 1fr"
            className="BuyerDownloads__table"
            emptyMessage="You have no downloads yet."
          />
        ) : (
          <div className="BuyerDownloads__empty">
            <h3>No downloads yet</h3>
            <p>Your purchased content will appear here once you make your first purchase.</p>
          </div>
        )}
      </SectionWrapper>
    </div>
  );
};

export default BuyerDownloads;

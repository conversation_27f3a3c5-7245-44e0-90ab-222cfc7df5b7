import api from './api';
import { DASHBOARD_ENDPOINTS, ORDER_ENDPOINTS, BID_ENDPOINTS, REQUEST_ENDPOINTS } from '../utils/constants';

/**
 * Dashboard Service for Buyer-specific data
 */

/**
 * Get buyer dashboard stats
 * @returns {Promise} Promise with dashboard stats
 */
export const getBuyerDashboardStats = async () => {
  try {
    const response = await api.get(`${DASHBOARD_ENDPOINTS.STATS}/buyer`);
    return response.data;
  } catch (error) {
    console.error('Error fetching buyer dashboard stats:', error);
    throw error;
  }
};

/**
 * Get buyer orders with pagination and filtering
 * @param {Object} params - Query parameters
 * @returns {Promise} Promise with orders data
 */
export const getBuyerOrders = async (params = {}) => {
  try {
    const response = await api.get(ORDER_ENDPOINTS.BUYER_ORDERS, { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching buyer orders:', error);
    throw error;
  }
};

/**
 * Get buyer downloads (completed orders with purchased content)
 * @param {Object} params - Query parameters
 * @returns {Promise} Promise with downloads data
 */
export const getBuyerDownloads = async (params = {}) => {
  try {
    const response = await api.get(ORDER_ENDPOINTS.BUYER_ORDERS, {
      params,
      headers: {
        Authorization: `Bearer ${localStorage.getItem('xosportshub_token')}`
      }
    });

    // Filter orders to only include completed payments (downloadable content)
    const orders = response.data?.data || response.data || [];
    console.log('Raw orders from API:', orders);
    console.log('Orders count:', orders.length);

    // Temporarily show all orders for debugging
    const completedOrders = orders.filter(order => order.paymentStatus === 'Completed');
    console.log('Completed orders:', completedOrders);
    console.log('Completed orders count:', completedOrders.length);

    // For debugging: show all orders if no completed ones
    const ordersToShow = completedOrders.length > 0 ? completedOrders : orders;
    console.log('Orders to show:', ordersToShow);

    const downloads = ordersToShow.map(order => {
      console.log('Processing order:', order._id, 'Payment Status:', order.paymentStatus, 'Content:', order.content?.title);
      return {
        id: order._id,
        orderId: order._id,
        title: order.content?.title || 'Unknown Content',
        coach: order.seller ? `${order.seller.firstName} ${order.seller.lastName}` : 'Unknown Coach',
        downloadDate: order.lastDownloaded ? new Date(order.lastDownloaded).toLocaleDateString() : new Date(order.createdAt).toLocaleDateString(),
        amount: order.amount,
        fileSize: order.content?.fileSize || 'Unknown',
        fileType: order.content?.contentType || 'Unknown',
        downloadCount: order.downloadCount || 0,
        thumbnail: order.content?.thumbnail || order.content?.thumbnailUrl,
        status: order.paymentStatus === 'Completed' ? 'Downloaded' : `Payment ${order.paymentStatus}`,
        paymentStatus: order.paymentStatus, // Add for debugging
        // Include full order and content data for download functionality
        order: order,
        content: order.content
      };
    });

    return downloads;
  } catch (error) {
    console.error('Error fetching buyer downloads:', error);
    return [];
  }
};

/**
 * Get buyer bids
 * @param {Object} params - Query parameters
 * @returns {Promise} Promise with bids data
 */
export const getBuyerBids = async (params = {}) => {
  try {
    const response = await api.get(BID_ENDPOINTS.USER_BIDS, {
      params,
      headers: {
        Authorization: `Bearer ${localStorage.getItem('xosportshub_token')}`
      }
    });
    return Array.isArray(response.data) ? response.data : [];
  } catch (error) {
    console.error('Error fetching buyer bids:', error);
    return [];
  }
};

/**
 * Get buyer requests
 * @param {Object} params - Query parameters
 * @returns {Promise} Promise with requests data
 */
export const getBuyerRequests = async (params = {}) => {
  try {
    const response = await api.get(REQUEST_ENDPOINTS.BUYER_REQUESTS, { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching buyer requests:', error);
    throw error;
  }
};

/**
 * Get buyer activity feed
 * @param {Object} params - Query parameters
 * @returns {Promise} Promise with activity data
 */
export const getBuyerActivity = async (params = {}) => {
  try {
    const response = await api.get(`${DASHBOARD_ENDPOINTS.ACTIVITY}/buyer`, { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching buyer activity:', error);
    throw error;
  }
};

/**
 * Get buyer notifications
 * @returns {Promise} Promise with notifications data
 */
export const getBuyerNotifications = async () => {
  try {
    const response = await api.get('/notifications/me');
    return response.data;
  } catch (error) {
    console.error('Error fetching buyer notifications:', error);
    throw error;
  }
};

/**
 * Mark notification as read
 * @param {string} notificationId - Notification ID
 * @returns {Promise} Promise with success response
 */
export const markNotificationAsRead = async (notificationId) => {
  try {
    const response = await api.put(`/notifications/${notificationId}/read`);
    return response.data;
  } catch (error) {
    console.error('Error marking notification as read:', error);
    throw error;
  }
};

/**
 * Get buyer wishlist
 * @returns {Promise} Promise with wishlist data
 */
export const getBuyerWishlist = async () => {
  try {
    const response = await api.get('/wishlist');
    return response.data;
  } catch (error) {
    console.error('Error fetching buyer wishlist:', error);
    throw error;
  }
};

export default {
  getBuyerDashboardStats,
  getBuyerOrders,
  getBuyerDownloads,
  getBuyerBids,
  getBuyerRequests,
  getBuyerActivity,
  getBuyerNotifications,
  markNotificationAsRead,
  getBuyerWishlist,
};
